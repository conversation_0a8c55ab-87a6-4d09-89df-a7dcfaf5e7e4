"use client"

import { useState, useEffect } from "react"
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Alert } from "react-native"
import { Ionicons } from "@expo/vector-icons"
import * as ImagePicker from "expo-image-picker"
import { supabase } from "../lib/supabase"
import { useAuth } from "../contexts/AuthContext"
import type { Post } from "../types"
import { Avatar } from "../components/ui/Avatar"
import { PostCard } from "../components/feed/PostCard"

export function ProfileScreen() {
  const { user, profile, updateProfile, signOut } = useAuth()
  const [posts, setPosts] = useState<Post[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)

  useEffect(() => {
    if (user) {
      fetchUserPosts()
    }
  }, [user])

  const fetchUserPosts = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from("posts")
        .select(`
          *,
          profiles (
            id,
            username,
            full_name,
            avatar_url,
            is_verified
          )
        `)
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })

      if (error) throw error
      setPosts(data || [])
    } catch (error) {
      console.error("Error fetching user posts:", error)
    } finally {
      setLoading(false)
    }
  }

  const uploadAvatar = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      })

      if (result.canceled) return

      setUploading(true)

      const response = await fetch(result.assets[0].uri)
      const blob = await response.blob()
      const fileExt = result.assets[0].uri.split(".").pop()
      const fileName = `${Date.now()}.${fileExt}`
      const filePath = `avatars/${user?.id}/${fileName}`

      const { error: uploadError } = await supabase.storage.from("media").upload(filePath, blob)

      if (uploadError) throw uploadError

      const { data } = supabase.storage.from("media").getPublicUrl(filePath)

      await updateProfile({ avatar_url: data.publicUrl })
    } catch (error: any) {
      Alert.alert("Error", error.message)
    } finally {
      setUploading(false)
    }
  }

  const handleSignOut = async () => {
    Alert.alert("Sign Out", "Are you sure you want to sign out?", [
      { text: "Cancel", style: "cancel" },
      { text: "Sign Out", style: "destructive", onPress: signOut },
    ])
  }

  const renderPost = ({ item }: { item: Post }) => <PostCard post={item} />

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.coverPhoto} />

      <View style={styles.profileInfo}>
        <TouchableOpacity onPress={uploadAvatar} disabled={uploading}>
          <Avatar
            uri={profile?.avatar_url}
            name={profile?.full_name || profile?.username}
            size={80}
            style={styles.avatar}
          />
          {uploading && (
            <View style={styles.uploadingOverlay}>
              <Ionicons name="camera" size={24} color="#FFFFFF" />
            </View>
          )}
        </TouchableOpacity>

        <View style={styles.actions}>
          <TouchableOpacity style={styles.editButton}>
            <Text style={styles.editButtonText}>Edit Profile</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.menuButton} onPress={handleSignOut}>
            <Ionicons name="ellipsis-horizontal" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.userDetails}>
        <View style={styles.nameRow}>
          <Text style={styles.fullName}>{profile?.full_name || profile?.username}</Text>
          {profile?.is_verified && <Ionicons name="checkmark-circle" size={20} color="#1DA1F2" />}
        </View>
        <Text style={styles.username}>@{profile?.username}</Text>

        {profile?.bio && <Text style={styles.bio}>{profile.bio}</Text>}

        {profile?.website && (
          <TouchableOpacity style={styles.websiteRow}>
            <Ionicons name="link" size={16} color="#8899a6" />
            <Text style={styles.website}>{profile.website}</Text>
          </TouchableOpacity>
        )}

        <View style={styles.joinedRow}>
          <Ionicons name="calendar-outline" size={16} color="#8899a6" />
          <Text style={styles.joinedText}>
            Joined{" "}
            {new Date(profile?.created_at || "").toLocaleDateString("en-US", {
              month: "long",
              year: "numeric",
            })}
          </Text>
        </View>

        <View style={styles.statsRow}>
          <TouchableOpacity style={styles.stat}>
            <Text style={styles.statNumber}>{profile?.following_count}</Text>
            <Text style={styles.statLabel}>Following</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.stat}>
            <Text style={styles.statNumber}>{profile?.followers_count}</Text>
            <Text style={styles.statLabel}>Followers</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.tabsContainer}>
        <TouchableOpacity style={[styles.tab, styles.activeTab]}>
          <Text style={[styles.tabText, styles.activeTabText]}>Posts</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.tab}>
          <Text style={styles.tabText}>Media</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.tab}>
          <Text style={styles.tabText}>Likes</Text>
        </TouchableOpacity>
      </View>
    </View>
  )

  return (
    <View style={styles.container}>
      <FlatList
        data={posts}
        renderItem={renderPost}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        showsVerticalScrollIndicator={false}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#15202b",
  },
  header: {
    backgroundColor: "#15202b",
  },
  coverPhoto: {
    height: 150,
    backgroundColor: "#192734",
  },
  profileInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    paddingHorizontal: 16,
    marginTop: -40,
  },
  avatar: {
    borderWidth: 4,
    borderColor: "#15202b",
  },
  uploadingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  actions: {
    flexDirection: "row",
    alignItems: "center",
  },
  editButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: "#38444d",
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
  },
  editButtonText: {
    color: "#FFFFFF",
    fontWeight: "600",
  },
  menuButton: {
    padding: 8,
  },
  userDetails: {
    paddingHorizontal: 16,
    paddingTop: 12,
  },
  nameRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  fullName: {
    fontSize: 20,
    fontWeight: "700",
    color: "#FFFFFF",
    marginRight: 4,
  },
  username: {
    fontSize: 16,
    color: "#8899a6",
    marginTop: 2,
  },
  bio: {
    fontSize: 16,
    color: "#FFFFFF",
    lineHeight: 22,
    marginTop: 12,
  },
  websiteRow: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  website: {
    fontSize: 16,
    color: "#1DA1F2",
    marginLeft: 4,
  },
  joinedRow: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  joinedText: {
    fontSize: 16,
    color: "#8899a6",
    marginLeft: 4,
  },
  statsRow: {
    flexDirection: "row",
    marginTop: 12,
  },
  stat: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 20,
  },
  statNumber: {
    fontSize: 16,
    fontWeight: "700",
    color: "#FFFFFF",
    marginRight: 4,
  },
  statLabel: {
    fontSize: 16,
    color: "#8899a6",
  },
  tabsContainer: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#38444d",
    marginTop: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: "center",
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#1DA1F2",
  },
  tabText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#8899a6",
  },
  activeTabText: {
    color: "#1DA1F2",
  },
})
