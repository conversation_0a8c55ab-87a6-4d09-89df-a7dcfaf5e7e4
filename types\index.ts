export interface Profile {
  id: string
  username: string
  full_name?: string
  bio?: string
  avatar_url?: string
  website?: string
  followers_count: number
  following_count: number
  posts_count: number
  is_verified: boolean
  is_private: boolean
  created_at: string
  updated_at: string
}

export interface Post {
  id: string
  user_id: string
  content: string
  image_url?: string
  hashtags?: string[]
  mentions?: string[]
  likes_count: number
  comments_count: number
  shares_count: number
  is_pinned: boolean
  created_at: string
  updated_at: string
  profiles?: Profile
  is_liked?: boolean
}

export interface Story {
  id: string
  user_id: string
  media_url: string
  media_type: "image" | "video"
  caption?: string
  views_count: number
  expires_at: string
  created_at: string
  profiles?: Profile
  is_viewed?: boolean
}

export interface Comment {
  id: string
  user_id: string
  post_id: string
  content: string
  likes_count: number
  created_at: string
  updated_at: string
  profiles?: Profile
}

export interface Message {
  id: string
  conversation_id: string
  sender_id: string
  content: string
  message_type: "text" | "image" | "video"
  media_url?: string
  is_read: boolean
  created_at: string
  profiles?: Profile
}

export interface Conversation {
  id: string
  created_at: string
  updated_at: string
  participants?: Profile[]
  last_message?: Message
}

export interface Notification {
  id: string
  user_id: string
  actor_id?: string
  type: "like" | "comment" | "follow" | "mention" | "story_view"
  post_id?: string
  comment_id?: string
  message?: string
  is_read: boolean
  created_at: string
  profiles?: Profile
}
