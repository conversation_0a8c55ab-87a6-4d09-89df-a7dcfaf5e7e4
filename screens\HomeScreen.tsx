"use client"

import { useState, useEffect, useCallback } from "react"
import { View, StyleSheet, FlatList, RefreshControl, Text } from "react-native"
import { supabase } from "../lib/supabase"
import { useAuth } from "../contexts/AuthContext"
import type { Post } from "../types"
import { PostCard } from "../components/feed/PostCard"
import { CreatePost } from "../components/feed/CreatePost"

export function HomeScreen() {
  const { user } = useAuth()
  const [posts, setPosts] = useState<Post[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const fetchPosts = useCallback(async () => {
    if (!user) return

    try {
      // Get posts from followed users and own posts
      const { data: followingData } = await supabase.from("follows").select("following_id").eq("follower_id", user.id)

      const followingIds = followingData?.map((f) => f.following_id) || []
      const userIds = [user.id, ...followingIds]

      const { data, error } = await supabase
        .from("posts")
        .select(`
          *,
          profiles (
            id,
            username,
            full_name,
            avatar_url,
            is_verified
          )
        `)
        .in("user_id", userIds)
        .order("created_at", { ascending: false })
        .limit(20)

      if (error) throw error

      // Check which posts are liked by current user
      const postIds = data?.map((p) => p.id) || []
      const { data: likesData } = await supabase
        .from("likes")
        .select("post_id")
        .eq("user_id", user.id)
        .in("post_id", postIds)

      const likedPostIds = new Set(likesData?.map((l) => l.post_id) || [])

      const postsWithLikes =
        data?.map((post) => ({
          ...post,
          is_liked: likedPostIds.has(post.id),
        })) || []

      setPosts(postsWithLikes)
    } catch (error) {
      console.error("Error fetching posts:", error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [user])

  useEffect(() => {
    fetchPosts()
  }, [fetchPosts])

  const onRefresh = () => {
    setRefreshing(true)
    fetchPosts()
  }

  const renderPost = ({ item }: { item: Post }) => (
    <PostCard
      post={item}
      onLike={(postId, isLiked) => {
        setPosts((prev) =>
          prev.map((p) =>
            p.id === postId
              ? {
                  ...p,
                  is_liked: isLiked,
                  likes_count: isLiked ? p.likes_count + 1 : p.likes_count - 1,
                }
              : p,
          ),
        )
      }}
    />
  )

  const renderHeader = () => <CreatePost onPostCreated={fetchPosts} />

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Loading feed...</Text>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={posts}
        renderItem={renderPost}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#1DA1F2" />}
        showsVerticalScrollIndicator={false}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#15202b",
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#8899a6",
    fontSize: 16,
  },
})
