"use client"

import { useState, useEffect } from "react"
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Image, Dimensions, Alert } from "react-native"
import { Ionicons } from "@expo/vector-icons"
import * as ImagePicker from "expo-image-picker"
import { supabase } from "../lib/supabase"
import { useAuth } from "../contexts/AuthContext"
import type { Story } from "../types"
import { Avatar } from "../components/ui/Avatar"

const { width } = Dimensions.get("window")

export function StoriesScreen() {
  const { user, profile } = useAuth()
  const [stories, setStories] = useState<Story[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStories()
  }, [])

  const fetchStories = async () => {
    try {
      const { data, error } = await supabase
        .from("stories")
        .select(`
          *,
          profiles (
            id,
            username,
            full_name,
            avatar_url
          )
        `)
        .gt("expires_at", new Date().toISOString())
        .order("created_at", { ascending: false })

      if (error) throw error
      setStories(data || [])
    } catch (error) {
      console.error("Error fetching stories:", error)
    } finally {
      setLoading(false)
    }
  }

  const createStory = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: true,
        aspect: [9, 16],
        quality: 0.8,
      })

      if (result.canceled) return

      const asset = result.assets[0]
      const response = await fetch(asset.uri)
      const blob = await response.blob()
      const fileExt = asset.uri.split(".").pop()
      const fileName = `${Date.now()}.${fileExt}`
      const filePath = `stories/${user?.id}/${fileName}`

      const { error: uploadError } = await supabase.storage.from("media").upload(filePath, blob)

      if (uploadError) throw uploadError

      const { data } = supabase.storage.from("media").getPublicUrl(filePath)

      const { error } = await supabase.from("stories").insert({
        user_id: user?.id,
        media_url: data.publicUrl,
        media_type: asset.type === "video" ? "video" : "image",
      })

      if (error) throw error

      fetchStories()
      Alert.alert("Success", "Story created!")
    } catch (error: any) {
      Alert.alert("Error", error.message)
    }
  }

  const renderStoryItem = ({ item }: { item: Story }) => (
    <TouchableOpacity style={styles.storyItem}>
      <Image source={{ uri: item.media_url }} style={styles.storyImage} />
      <View style={styles.storyOverlay}>
        <Avatar uri={item.profiles?.avatar_url} name={item.profiles?.full_name || item.profiles?.username} size={32} />
        <Text style={styles.storyUsername}>{item.profiles?.username}</Text>
      </View>
    </TouchableOpacity>
  )

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity style={styles.createStory} onPress={createStory}>
        <View style={styles.createStoryContent}>
          <Avatar uri={profile?.avatar_url} name={profile?.full_name || profile?.username} size={60} />
          <View style={styles.addIcon}>
            <Ionicons name="add" size={20} color="#FFFFFF" />
          </View>
        </View>
        <Text style={styles.createStoryText}>Your Story</Text>
      </TouchableOpacity>
    </View>
  )

  return (
    <View style={styles.container}>
      <FlatList
        data={stories}
        renderItem={renderStoryItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        numColumns={2}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.content}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#15202b",
  },
  content: {
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  createStory: {
    alignItems: "center",
    marginBottom: 16,
  },
  createStoryContent: {
    position: "relative",
  },
  addIcon: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: "#1DA1F2",
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: "#15202b",
  },
  createStoryText: {
    color: "#FFFFFF",
    fontSize: 14,
    marginTop: 8,
  },
  storyItem: {
    width: (width - 48) / 2,
    height: 200,
    marginHorizontal: 8,
    marginBottom: 16,
    borderRadius: 12,
    overflow: "hidden",
  },
  storyImage: {
    width: "100%",
    height: "100%",
  },
  storyOverlay: {
    position: "absolute",
    top: 12,
    left: 12,
    alignItems: "center",
  },
  storyUsername: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
    marginTop: 4,
    textShadowColor: "rgba(0, 0, 0, 0.8)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
})
