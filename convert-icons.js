const fs = require('fs');
const sharp = require('sharp');

async function convertSvgToPng() {
  try {
    // Convert main icon (1024x1024)
    await sharp('assets/icon.svg')
      .resize(1024, 1024)
      .png()
      .toFile('assets/icon.png');
    console.log('✓ Created icon.png');

    // Convert splash screen (1284x2778)
    await sharp('assets/splash.svg')
      .resize(1284, 2778)
      .png()
      .toFile('assets/splash.png');
    console.log('✓ Created splash.png');

    // Convert adaptive icon (1024x1024)
    await sharp('assets/adaptive-icon.svg')
      .resize(1024, 1024)
      .png()
      .toFile('assets/adaptive-icon.png');
    console.log('✓ Created adaptive-icon.png');

    // Convert favicon (32x32)
    await sharp('assets/favicon.svg')
      .resize(32, 32)
      .png()
      .toFile('assets/favicon.png');
    console.log('✓ Created favicon.png');

    console.log('All icons converted successfully!');
  } catch (error) {
    console.error('Error converting icons:', error);
  }
}

convertSvgToPng();
