"use client"

import { useState } from "react"
import { View, Text, StyleSheet, SafeAreaView, KeyboardAvoidingView, Platform, Alert } from "react-native"
import { supabase } from "../../lib/supabase"
import { Button } from "../ui/button"
import { Input } from "../ui/input"

export function AuthScreen() {
  const [isSignUp, setIsSignUp] = useState(false)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [username, setUsername] = useState("")
  const [fullName, setFullName] = useState("")
  const [loading, setLoading] = useState(false)

  const handleAuth = async () => {
    if (!email || !password) {
      Alert.alert("Error", "Please fill in all fields")
      return
    }

    if (isSignUp && !username) {
      Alert.alert("Error", "Username is required")
      return
    }

    setLoading(true)

    try {
      if (isSignUp) {
        const { error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              username,
              full_name: fullName,
            },
          },
        })
        if (error) throw error
        Alert.alert("Success", "Check your email for verification link!")
      } else {
        const { error } = await supabase.auth.signInWithPassword({
          email,
          password,
        })
        if (error) throw error
      }
    } catch (error: any) {
      Alert.alert("Error", error.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : "height"} style={styles.keyboardView}>
        <View style={styles.content}>
          <Text style={styles.title}>{isSignUp ? "Create Account" : "Welcome Back"}</Text>
          <Text style={styles.subtitle}>{isSignUp ? "Join the conversation" : "Sign in to continue"}</Text>

          <View style={styles.form}>
            {isSignUp && (
              <>
                <Input
                  label="Username"
                  value={username}
                  onChangeText={setUsername}
                  placeholder="Enter your username"
                  autoCapitalize="none"
                />
                <Input
                  label="Full Name"
                  value={fullName}
                  onChangeText={setFullName}
                  placeholder="Enter your full name"
                />
              </>
            )}
            <Input
              label="Email"
              value={email}
              onChangeText={setEmail}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
            />
            <Input
              label="Password"
              value={password}
              onChangeText={setPassword}
              placeholder="Enter your password"
              secureTextEntry
            />

            <Button
              title={isSignUp ? "Sign Up" : "Sign In"}
              onPress={handleAuth}
              loading={loading}
              style={styles.authButton}
            />

            <Button
              title={isSignUp ? "Already have an account? Sign In" : "Don't have an account? Sign Up"}
              onPress={() => setIsSignUp(!isSignUp)}
              variant="ghost"
              style={styles.switchButton}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#15202b",
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: "center",
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#FFFFFF",
    textAlign: "center",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#8899a6",
    textAlign: "center",
    marginBottom: 48,
  },
  form: {
    width: "100%",
  },
  authButton: {
    marginTop: 24,
  },
  switchButton: {
    marginTop: 16,
  },
})
