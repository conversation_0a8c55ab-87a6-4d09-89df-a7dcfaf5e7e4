"use client"
import { StatusBar } from "expo-status-bar"
import { NavigationContainer } from "@react-navigation/native"
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { Ionicons } from "@expo/vector-icons"
import { AuthProvider, useAuth } from "./contexts/AuthContext"
import { AuthScreen } from "./components/auth/AuthScreen"
import { HomeScreen } from "./screens/HomeScreen"
import { ExploreScreen } from "./screens/ExploreScreen"
import { StoriesScreen } from "./screens/StoriesScreen"
import { NotificationsScreen } from "./screens/NotificationsScreen"
import { ProfileScreen } from "./screens/ProfileScreen"

const Tab = createBottomTabNavigator()

function AppNavigator() {
  const { session, loading } = useAuth()

  if (loading) {
    return null // You could add a loading screen here
  }

  if (!session) {
    return <AuthScreen />
  }

  return (
    <NavigationContainer>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused, color, size }) => {
            let iconName: keyof typeof Ionicons.glyphMap

            if (route.name === "Home") {
              iconName = focused ? "home" : "home-outline"
            } else if (route.name === "Explore") {
              iconName = focused ? "search" : "search-outline"
            } else if (route.name === "Stories") {
              iconName = focused ? "play-circle" : "play-circle-outline"
            } else if (route.name === "Notifications") {
              iconName = focused ? "notifications" : "notifications-outline"
            } else if (route.name === "Profile") {
              iconName = focused ? "person" : "person-outline"
            } else {
              iconName = "home-outline"
            }

            return <Ionicons name={iconName} size={size} color={color} />
          },
          tabBarActiveTintColor: "#1DA1F2",
          tabBarInactiveTintColor: "#8899a6",
          tabBarStyle: {
            backgroundColor: "#15202b",
            borderTopColor: "#38444d",
            borderTopWidth: 1,
          },
          headerStyle: {
            backgroundColor: "#15202b",
            borderBottomColor: "#38444d",
            borderBottomWidth: 1,
          },
          headerTintColor: "#FFFFFF",
          headerTitleStyle: {
            fontWeight: "700",
          },
        })}
      >
        <Tab.Screen name="Home" component={HomeScreen} />
        <Tab.Screen name="Explore" component={ExploreScreen} />
        <Tab.Screen name="Stories" component={StoriesScreen} />
        <Tab.Screen name="Notifications" component={NotificationsScreen} />
        <Tab.Screen name="Profile" component={ProfileScreen} />
      </Tab.Navigator>
    </NavigationContainer>
  )
}

export default function App() {
  return (
    <AuthProvider>
      <StatusBar style="light" backgroundColor="#15202b" />
      <AppNavigator />
    </AuthProvider>
  )
}
