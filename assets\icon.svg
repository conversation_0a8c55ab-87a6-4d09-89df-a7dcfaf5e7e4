<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main background -->
  <circle cx="512" cy="512" r="512" fill="url(#bgGradient)"/>
  
  <!-- Chat bubble main -->
  <path d="M200 300 C200 250, 250 200, 300 200 L700 200 C750 200, 800 250, 800 300 L800 500 C800 550, 750 600, 700 600 L400 600 L250 750 L250 600 C200 600, 200 550, 200 500 Z" fill="url(#iconGradient)" stroke="none"/>
  
  <!-- Connection dots representing social network -->
  <circle cx="350" cy="350" r="25" fill="#667eea"/>
  <circle cx="500" cy="350" r="25" fill="#667eea"/>
  <circle cx="650" cy="350" r="25" fill="#667eea"/>
  
  <!-- Connection lines -->
  <line x1="375" y1="350" x2="475" y2="350" stroke="#667eea" stroke-width="8" stroke-linecap="round"/>
  <line x1="525" y1="350" x2="625" y2="350" stroke="#667eea" stroke-width="8" stroke-linecap="round"/>
  
  <!-- Heart icon for likes/social interaction -->
  <path d="M450 450 C450 430, 470 410, 490 410 C510 410, 530 430, 530 450 C530 470, 490 510, 490 510 C490 510, 450 470, 450 450 Z" fill="#e53e3e"/>
  <path d="M470 450 C470 430, 490 410, 510 410 C530 410, 550 430, 550 450 C550 470, 510 510, 510 510 C510 510, 470 470, 470 450 Z" fill="#e53e3e"/>
  
  <!-- Plus icon for adding content -->
  <rect x="580" y="470" width="40" height="8" rx="4" fill="#38a169"/>
  <rect x="596" y="454" width="8" height="40" rx="4" fill="#38a169"/>
</svg>
