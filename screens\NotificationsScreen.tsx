"use client"

import { useState, useEffect } from "react"
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from "react-native"
import { Ionicons } from "@expo/vector-icons"
import { supabase } from "../lib/supabase"
import { useAuth } from "../contexts/AuthContext"
import type { Notification } from "../types"
import { Avatar } from "../components/ui/Avatar"

export function NotificationsScreen() {
  const { user } = useAuth()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchNotifications()
    }
  }, [user])

  const fetchNotifications = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from("notifications")
        .select(`
          *,
          profiles (
            id,
            username,
            full_name,
            avatar_url,
            is_verified
          )
        `)
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })
        .limit(50)

      if (error) throw error
      setNotifications(data || [])

      // Mark notifications as read
      await supabase.from("notifications").update({ is_read: true }).eq("user_id", user.id).eq("is_read", false)
    } catch (error) {
      console.error("Error fetching notifications:", error)
    } finally {
      setLoading(false)
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "like":
        return <Ionicons name="heart" size={20} color="#f91880" />
      case "comment":
        return <Ionicons name="chatbubble" size={20} color="#1DA1F2" />
      case "follow":
        return <Ionicons name="person-add" size={20} color="#1DA1F2" />
      case "mention":
        return <Ionicons name="at" size={20} color="#1DA1F2" />
      case "story_view":
        return <Ionicons name="eye" size={20} color="#8899a6" />
      default:
        return <Ionicons name="notifications" size={20} color="#8899a6" />
    }
  }

  const getNotificationText = (notification: Notification) => {
    const actorName = notification.profiles?.full_name || notification.profiles?.username || "Someone"

    switch (notification.type) {
      case "like":
        return `${actorName} liked your post`
      case "comment":
        return `${actorName} commented on your post`
      case "follow":
        return `${actorName} started following you`
      case "mention":
        return `${actorName} mentioned you in a post`
      case "story_view":
        return `${actorName} viewed your story`
      default:
        return notification.message || "New notification"
    }
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
      return `${diffInMinutes}m`
    } else if (diffInHours < 24) {
      return `${diffInHours}h`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d`
    }
  }

  const renderNotification = ({ item }: { item: Notification }) => (
    <TouchableOpacity style={styles.notificationItem}>
      <View style={styles.iconContainer}>{getNotificationIcon(item.type)}</View>

      <Avatar
        uri={item.profiles?.avatar_url}
        name={item.profiles?.full_name || item.profiles?.username}
        size={40}
        style={styles.avatar}
      />

      <View style={styles.content}>
        <Text style={styles.text}>{getNotificationText(item)}</Text>
        <Text style={styles.time}>{formatTime(item.created_at)}</Text>
      </View>
    </TouchableOpacity>
  )

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Loading notifications...</Text>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={notifications}
        renderItem={renderNotification}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="notifications-outline" size={64} color="#38444d" />
            <Text style={styles.emptyText}>No notifications yet</Text>
            <Text style={styles.emptySubtext}>When someone likes, comments, or follows you, you'll see it here</Text>
          </View>
        }
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#15202b",
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#8899a6",
    fontSize: 16,
  },
  notificationItem: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#38444d",
    alignItems: "flex-start",
  },
  iconContainer: {
    marginRight: 12,
    marginTop: 8,
  },
  avatar: {
    marginRight: 12,
  },
  content: {
    flex: 1,
  },
  text: {
    fontSize: 16,
    color: "#FFFFFF",
    lineHeight: 20,
  },
  time: {
    fontSize: 14,
    color: "#8899a6",
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 32,
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 24,
    fontWeight: "700",
    color: "#FFFFFF",
    marginTop: 16,
    textAlign: "center",
  },
  emptySubtext: {
    fontSize: 16,
    color: "#8899a6",
    marginTop: 8,
    textAlign: "center",
    lineHeight: 22,
  },
})
