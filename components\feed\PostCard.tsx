"use client"

import { useState } from "react"
import { View, Text, StyleSheet, TouchableOpacity, Image, Alert } from "react-native"
import { Ionicons } from "@expo/vector-icons"
import type { Post } from "../../types"
import { Avatar } from "../ui/Avatar"
import { supabase } from "../../lib/supabase"
import { useAuth } from "../../contexts/AuthContext"

interface PostCardProps {
  post: Post
  onPress?: () => void
  onLike?: (postId: string, isLiked: boolean) => void
  onComment?: () => void
  onShare?: () => void
}

export function PostCard({ post, onPress, onLike, onComment, onShare }: PostCardProps) {
  const { user } = useAuth()
  const [isLiked, setIsLiked] = useState(post.is_liked || false)
  const [likesCount, setLikesCount] = useState(post.likes_count)
  const [loading, setLoading] = useState(false)

  const handleLike = async () => {
    if (!user || loading) return

    setLoading(true)
    try {
      if (isLiked) {
        const { error } = await supabase.from("likes").delete().eq("user_id", user.id).eq("post_id", post.id)

        if (error) throw error
        setIsLiked(false)
        setLikesCount((prev) => prev - 1)
      } else {
        const { error } = await supabase.from("likes").insert({ user_id: user.id, post_id: post.id })

        if (error) throw error
        setIsLiked(true)
        setLikesCount((prev) => prev + 1)
      }

      onLike?.(post.id, !isLiked)
    } catch (error: any) {
      Alert.alert("Error", error.message)
    } finally {
      setLoading(false)
    }
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
      return `${diffInMinutes}m`
    } else if (diffInHours < 24) {
      return `${diffInHours}h`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d`
    }
  }

  const renderContent = (text: string) => {
    const parts = text.split(/(\s+)/)
    return parts.map((part, index) => {
      if (part.startsWith("#")) {
        return (
          <Text key={index} style={styles.hashtag}>
            {part}
          </Text>
        )
      } else if (part.startsWith("@")) {
        return (
          <Text key={index} style={styles.mention}>
            {part}
          </Text>
        )
      }
      return <Text key={index}>{part}</Text>
    })
  }

  return (
    <TouchableOpacity style={styles.container} onPress={onPress} activeOpacity={0.95}>
      <View style={styles.header}>
        <Avatar uri={post.profiles?.avatar_url} name={post.profiles?.full_name || post.profiles?.username} size={48} />
        <View style={styles.userInfo}>
          <View style={styles.nameRow}>
            <Text style={styles.fullName}>{post.profiles?.full_name || post.profiles?.username}</Text>
            {post.profiles?.is_verified && <Ionicons name="checkmark-circle" size={16} color="#1DA1F2" />}
            <Text style={styles.username}>@{post.profiles?.username}</Text>
            <Text style={styles.separator}>·</Text>
            <Text style={styles.time}>{formatTime(post.created_at)}</Text>
          </View>
        </View>
      </View>

      <View style={styles.content}>
        <Text style={styles.text}>{renderContent(post.content)}</Text>
        {post.image_url && <Image source={{ uri: post.image_url }} style={styles.image} />}
      </View>

      <View style={styles.actions}>
        <TouchableOpacity style={styles.action} onPress={onComment}>
          <Ionicons name="chatbubble-outline" size={18} color="#8899a6" />
          <Text style={styles.actionText}>{post.comments_count}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.action} onPress={onShare}>
          <Ionicons name="repeat-outline" size={18} color="#8899a6" />
          <Text style={styles.actionText}>{post.shares_count}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.action} onPress={handleLike} disabled={loading}>
          <Ionicons name={isLiked ? "heart" : "heart-outline"} size={18} color={isLiked ? "#f91880" : "#8899a6"} />
          <Text style={[styles.actionText, isLiked && styles.likedText]}>{likesCount}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.action}>
          <Ionicons name="share-outline" size={18} color="#8899a6" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#15202b",
    borderBottomWidth: 1,
    borderBottomColor: "#38444d",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  header: {
    flexDirection: "row",
    marginBottom: 12,
  },
  userInfo: {
    flex: 1,
    marginLeft: 12,
  },
  nameRow: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
  },
  fullName: {
    fontSize: 16,
    fontWeight: "700",
    color: "#FFFFFF",
    marginRight: 4,
  },
  username: {
    fontSize: 16,
    color: "#8899a6",
    marginLeft: 4,
  },
  separator: {
    fontSize: 16,
    color: "#8899a6",
    marginHorizontal: 4,
  },
  time: {
    fontSize: 16,
    color: "#8899a6",
  },
  content: {
    marginLeft: 60,
  },
  text: {
    fontSize: 16,
    lineHeight: 24,
    color: "#FFFFFF",
    marginBottom: 12,
  },
  hashtag: {
    color: "#1DA1F2",
  },
  mention: {
    color: "#1DA1F2",
  },
  image: {
    width: "100%",
    height: 200,
    borderRadius: 12,
    marginTop: 8,
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginLeft: 60,
    marginTop: 12,
    maxWidth: 300,
  },
  action: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionText: {
    fontSize: 14,
    color: "#8899a6",
    marginLeft: 4,
  },
  likedText: {
    color: "#f91880",
  },
})
