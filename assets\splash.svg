<svg width="1284" height="2778" viewBox="0 0 1284 2778" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="splashGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#15202b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1a2332;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#15202b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1284" height="2778" fill="url(#splashGradient)"/>
  
  <!-- Main logo container -->
  <g transform="translate(642, 1389)">
    <!-- Logo circle -->
    <circle cx="0" cy="0" r="120" fill="url(#logoGradient)"/>
    
    <!-- Chat bubble -->
    <path d="M-60 -40 C-60 -60, -40 -80, -20 -80 L40 -80 C60 -80, 80 -60, 80 -40 L80 20 C80 40, 60 60, 40 60 L-20 60 L-50 90 L-50 60 C-60 60, -60 40, -60 20 Z" fill="white"/>
    
    <!-- Connection dots -->
    <circle cx="-30" cy="-20" r="8" fill="#667eea"/>
    <circle cx="0" cy="-20" r="8" fill="#667eea"/>
    <circle cx="30" cy="-20" r="8" fill="#667eea"/>
    
    <!-- Connection lines -->
    <line x1="-22" y1="-20" x2="-8" y2="-20" stroke="#667eea" stroke-width="3" stroke-linecap="round"/>
    <line x1="8" y1="-20" x2="22" y2="-20" stroke="#667eea" stroke-width="3" stroke-linecap="round"/>
    
    <!-- Heart -->
    <path d="M-10 10 C-10 5, -5 0, 0 0 C5 0, 10 5, 10 10 C10 15, 0 25, 0 25 C0 25, -10 15, -10 10 Z" fill="#e53e3e"/>
  </g>
  
  <!-- App name -->
  <text x="642" y="1600" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="48" font-weight="bold">Yappers</text>
  <text x="642" y="1650" text-anchor="middle" fill="#9ca3af" font-family="Arial, sans-serif" font-size="24">Connect • Share • Engage</text>
</svg>
