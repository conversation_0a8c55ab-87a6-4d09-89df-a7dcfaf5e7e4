"use client"

import { useState } from "react"
import { View, Text, StyleSheet, TextInput, TouchableOpacity, Alert, Image } from "react-native"
import { Ionicons } from "@expo/vector-icons"
import * as ImagePicker from "expo-image-picker"
import { supabase } from "../../lib/supabase"
import { useAuth } from "../../contexts/AuthContext"
import { Avatar } from "../ui/avatar"
import { Button } from "../ui/button"

interface CreatePostProps {
  onPostCreated?: () => void
}

export function CreatePost({ onPostCreated }: CreatePostProps) {
  const { user, profile } = useAuth()
  const [content, setContent] = useState("")
  const [image, setImage] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [16, 9],
      quality: 0.8,
    })

    if (!result.canceled) {
      setImage(result.assets[0].uri)
    }
  }

  const uploadImage = async (uri: string) => {
    const response = await fetch(uri)
    const blob = await response.blob()
    const fileExt = uri.split(".").pop()
    const fileName = `${Date.now()}.${fileExt}`
    const filePath = `posts/${user?.id}/${fileName}`

    const { error: uploadError } = await supabase.storage.from("media").upload(filePath, blob)

    if (uploadError) throw uploadError

    const { data } = supabase.storage.from("media").getPublicUrl(filePath)

    return data.publicUrl
  }

  const extractHashtagsAndMentions = (text: string) => {
    const hashtags = text.match(/#\w+/g) || []
    const mentions = text.match(/@\w+/g) || []
    return {
      hashtags: hashtags.map((tag) => tag.slice(1)),
      mentions: mentions.map((mention) => mention.slice(1)),
    }
  }

  const handlePost = async () => {
    if (!content.trim()) {
      Alert.alert("Error", "Please write something")
      return
    }

    if (!user) return

    setLoading(true)
    try {
      let imageUrl = null
      if (image) {
        imageUrl = await uploadImage(image)
      }

      const { hashtags, mentions } = extractHashtagsAndMentions(content)

      const { error } = await supabase.from("posts").insert({
        user_id: user.id,
        content: content.trim(),
        image_url: imageUrl,
        hashtags,
        mentions,
      })

      if (error) throw error

      setContent("")
      setImage(null)
      onPostCreated?.()
      Alert.alert("Success", "Post created!")
    } catch (error: any) {
      Alert.alert("Error", error.message)
    } finally {
      setLoading(false)
    }
  }

  const characterCount = content.length
  const maxCharacters = 280

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Avatar uri={profile?.avatar_url} name={profile?.full_name || profile?.username} size={48} />
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="What's happening?"
            placeholderTextColor="#8899a6"
            multiline
            value={content}
            onChangeText={setContent}
            maxLength={maxCharacters}
          />
          {image && (
            <View style={styles.imageContainer}>
              <Image source={{ uri: image }} style={styles.previewImage} />
              <TouchableOpacity style={styles.removeImage} onPress={() => setImage(null)}>
                <Ionicons name="close-circle" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>

      <View style={styles.footer}>
        <View style={styles.actions}>
          <TouchableOpacity style={styles.mediaButton} onPress={pickImage}>
            <Ionicons name="image-outline" size={20} color="#1DA1F2" />
          </TouchableOpacity>
        </View>

        <View style={styles.postSection}>
          <Text
            style={[
              styles.characterCount,
              characterCount > maxCharacters * 0.8 && styles.warningCount,
              characterCount >= maxCharacters && styles.errorCount,
            ]}
          >
            {maxCharacters - characterCount}
          </Text>
          <Button
            title="Post"
            onPress={handlePost}
            loading={loading}
            disabled={!content.trim() || characterCount > maxCharacters}
            size="small"
          />
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#15202b",
    borderBottomWidth: 1,
    borderBottomColor: "#38444d",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  header: {
    flexDirection: "row",
  },
  inputContainer: {
    flex: 1,
    marginLeft: 12,
  },
  input: {
    fontSize: 18,
    color: "#FFFFFF",
    minHeight: 60,
    textAlignVertical: "top",
  },
  imageContainer: {
    position: "relative",
    marginTop: 12,
  },
  previewImage: {
    width: "100%",
    height: 200,
    borderRadius: 12,
  },
  removeImage: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    borderRadius: 12,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 12,
    marginLeft: 60,
  },
  actions: {
    flexDirection: "row",
  },
  mediaButton: {
    padding: 8,
  },
  postSection: {
    flexDirection: "row",
    alignItems: "center",
  },
  characterCount: {
    fontSize: 14,
    color: "#8899a6",
    marginRight: 12,
  },
  warningCount: {
    color: "#ffad1f",
  },
  errorCount: {
    color: "#f91880",
  },
})
