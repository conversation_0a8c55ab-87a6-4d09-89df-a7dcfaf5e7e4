"use client"

import { useState, useEffect } from "react"
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput } from "react-native"
import { Ionicons } from "@expo/vector-icons"
import { supabase } from "../lib/supabase"
import type { Profile, Post } from "../types"
import { Avatar } from "../components/ui/Avatar"
import { PostCard } from "../components/feed/PostCard"

export function ExploreScreen() {
  const [activeTab, setActiveTab] = useState<"trending" | "users">("trending")
  const [searchQuery, setSearchQuery] = useState("")
  const [trendingPosts, setTrendingPosts] = useState<Post[]>([])
  const [suggestedUsers, setSuggestedUsers] = useState<Profile[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchTrendingPosts()
    fetchSuggestedUsers()
  }, [])

  const fetchTrendingPosts = async () => {
    try {
      const { data, error } = await supabase
        .from("posts")
        .select(`
          *,
          profiles (
            id,
            username,
            full_name,
            avatar_url,
            is_verified
          )
        `)
        .order("likes_count", { ascending: false })
        .limit(20)

      if (error) throw error
      setTrendingPosts(data || [])
    } catch (error) {
      console.error("Error fetching trending posts:", error)
    }
  }

  const fetchSuggestedUsers = async () => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .order("followers_count", { ascending: false })
        .limit(20)

      if (error) throw error
      setSuggestedUsers(data || [])
    } catch (error) {
      console.error("Error fetching suggested users:", error)
    } finally {
      setLoading(false)
    }
  }

  const searchUsers = async (query: string) => {
    if (!query.trim()) {
      fetchSuggestedUsers()
      return
    }

    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .or(`username.ilike.%${query}%,full_name.ilike.%${query}%`)
        .limit(20)

      if (error) throw error
      setSuggestedUsers(data || [])
    } catch (error) {
      console.error("Error searching users:", error)
    }
  }

  const renderUserItem = ({ item }: { item: Profile }) => (
    <TouchableOpacity style={styles.userItem}>
      <Avatar uri={item.avatar_url} name={item.full_name || item.username} size={48} />
      <View style={styles.userInfo}>
        <View style={styles.userNameRow}>
          <Text style={styles.fullName}>{item.full_name || item.username}</Text>
          {item.is_verified && <Ionicons name="checkmark-circle" size={16} color="#1DA1F2" />}
        </View>
        <Text style={styles.username}>@{item.username}</Text>
        {item.bio && <Text style={styles.bio}>{item.bio}</Text>}
        <Text style={styles.followers}>{item.followers_count} followers</Text>
      </View>
    </TouchableOpacity>
  )

  const renderPostItem = ({ item }: { item: Post }) => <PostCard post={item} />

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#8899a6" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search"
            placeholderTextColor="#8899a6"
            value={searchQuery}
            onChangeText={(text) => {
              setSearchQuery(text)
              if (activeTab === "users") {
                searchUsers(text)
              }
            }}
          />
        </View>
      </View>

      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, activeTab === "trending" && styles.activeTab]}
          onPress={() => setActiveTab("trending")}
        >
          <Text style={[styles.tabText, activeTab === "trending" && styles.activeTabText]}>Trending</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === "users" && styles.activeTab]}
          onPress={() => setActiveTab("users")}
        >
          <Text style={[styles.tabText, activeTab === "users" && styles.activeTabText]}>People</Text>
        </TouchableOpacity>
      </View>

      {activeTab === "trending" ? (
        <FlatList
          data={trendingPosts}
          renderItem={renderPostItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <FlatList
          data={suggestedUsers}
          renderItem={renderUserItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#15202b",
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#38444d",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#192734",
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: "#FFFFFF",
  },
  tabs: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#38444d",
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: "center",
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#1DA1F2",
  },
  tabText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#8899a6",
  },
  activeTabText: {
    color: "#1DA1F2",
  },
  userItem: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#38444d",
  },
  userInfo: {
    flex: 1,
    marginLeft: 12,
  },
  userNameRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  fullName: {
    fontSize: 16,
    fontWeight: "700",
    color: "#FFFFFF",
    marginRight: 4,
  },
  username: {
    fontSize: 14,
    color: "#8899a6",
    marginTop: 2,
  },
  bio: {
    fontSize: 14,
    color: "#FFFFFF",
    marginTop: 4,
    lineHeight: 18,
  },
  followers: {
    fontSize: 14,
    color: "#8899a6",
    marginTop: 4,
  },
})
